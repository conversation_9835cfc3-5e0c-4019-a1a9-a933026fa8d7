# 🚀 Script de Despliegue Automático

Script para desplegar automáticamente la función Lambda `spreadsheet_generator` con limpieza automática de archivos temporales.

## 📋 Requisitos

- **AWS CLI** instalado y configurado
- **Permisos** para actualizar la función Lambda
- **Node.js** y dependencias instaladas (`npm install`)

## 🔧 Uso

```bash
# Desde el directorio lambda
cd lambda
./deploy.sh
```

## ✨ Características del Script

### 🛡️ Verificaciones Automáticas
- ✅ Verifica que AWS CLI esté instalado
- ✅ Verifica credenciales AWS válidas
- ✅ Confirma que estás en el directorio correcto
- ✅ Valida el estado de la función después del despliegue

### 📦 Proceso de Despliegue
1. **Creación del ZIP**: Empaqueta `lambda-handler.js`, `node_modules/` y `package.json`
2. **Actualización**: Despliega a `arn:aws:lambda:us-east-1:165354057769:function:spreadsheet_generator`
3. **Verificación**: Confirma que la actualización fue exitosa
4. **Información**: Muestra detalles de la función actualizada

### 🧹 Limpieza Automática
- ✅ Elimina archivos temporales al finalizar exitosamente
- ✅ Limpieza automática incluso si hay errores (trap EXIT)
- ✅ Logging detallado del proceso de limpieza

### 🎨 Output Colorizado
- 🔵 **Azul**: Logs informativos
- 🟢 **Verde**: Operaciones exitosas
- 🟡 **Amarillo**: Advertencias
- 🔴 **Rojo**: Errores

## 📊 Ejemplo de Salida

```
[2025-07-09 10:19:47] Iniciando despliegue de Lambda...
[2025-07-09 10:19:47] Creando archivo ZIP...
✅ Archivo ZIP creado: planilla-lambda.zip
[2025-07-09 10:19:50] Tamaño del archivo:  24M
[2025-07-09 10:19:50] Actualizando función Lambda...
✅ Función Lambda actualizada exitosamente
[2025-07-09 10:20:05] Verificando estado de la función...
✅ Función en estado: Successful

✅ 🚀 DESPLIEGUE COMPLETADO EXITOSAMENTE

📋 Información de la función:
------------------------------------------------------------
|                        GetFunction                       |
+----------+--------------------------------+--------------+
| CodeSize |         LastModified           |   Runtime    |
+----------+--------------------------------+--------------+
|  24031411|  2025-07-09T15:20:04.000+0000  |  nodejs22.x  |
+----------+--------------------------------+--------------+

🔗 ARN: arn:aws:lambda:us-east-1:165354057769:function:spreadsheet_generator

✨ Funcionalidades implementadas:
   • Soporte para logo en base64 (PNG)
   • Manejo de logo vacío con espacio apropiado
   • Alineación perfecta de tablas con headers
   • Márgenes optimizados para máximo aprovechamiento

✅ Despliegue finalizado. Archivos temporales limpiados.
```

## 🔧 Configuración

Para cambiar la función Lambda de destino, edita la variable en el script:

```bash
FUNCTION_ARN="arn:aws:lambda:us-east-1:165354057769:function:spreadsheet_generator"
```

## 🚨 Manejo de Errores

El script incluye manejo robusto de errores:
- **Salida inmediata** si cualquier comando falla (`set -e`)
- **Limpieza automática** en caso de error o interrupción
- **Mensajes descriptivos** para cada tipo de error
- **Timeout de 60 segundos** para operaciones de AWS

## 📝 Logs

Todos los pasos del despliegue se registran con timestamps para facilitar el debugging y seguimiento del proceso.
