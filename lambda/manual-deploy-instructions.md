# Instrucciones de Despliegue - Lambda Planilla PDF

## Archivos creados:
- `lambda-handler.js` - Función principal de Lambda
- `package-lambda.json` - Dependencias para Lambda
- `test-local.js` - Prueba local
- `deploy-instructions.md` - Este archivo

## Pasos para desplegar:

### 1. Preparar el paquete Lambda
```bash
# Copiar package.json para Lambda
cp package-lambda.json package.json

# Instalar dependencias
npm install

# Crear ZIP para Lambda
zip -r planilla-lambda.zip lambda-handler.js node_modules/ package.json
```

### 2. Crear función Lambda en AWS
```bash
# Usando AWS CLI
aws lambda create-function \
  --function-name planilla-pdf-generator \
  --runtime nodejs18.x \
  --role arn:aws:iam::TU-ACCOUNT:role/lambda-execution-role \
  --handler lambda-handler.handler \
  --zip-file fileb://planilla-lambda.zip \
  --timeout 30 \
  --memory-size 512
```

### 3. Configurar permisos IAM
La función necesita:
- `AWSLambdaBasicExecutionRole` (logs)
- `AmazonS3FullAccess` (si usas S3)

### 4. Probar localmente
```bash
node test-local.js
```

## Uso de la Lambda:

### Evento básico (retorna PDF en base64):
```json
{
  "planillaData": {
    "codigo": "25897",
    "version": "01.05",
    "fecha": "2022-03-19 10:25:15",
    "consecutivo": "0009",
    "mensajero": "Alexandra Yohana Valencia Florez",
    "registros": [...]
  }
}
```

### Evento con S3 (sube PDF a bucket):
```json
{
  "planillaData": {...},
  "uploadToS3": true,
  "bucketName": "mi-bucket-planillas"
}
```

## Respuestas:

### PDF en base64:
```json
{
  "statusCode": 200,
  "headers": {
    "Content-Type": "application/pdf",
    "Content-Disposition": "attachment; filename=\"planilla_distribucion.pdf\""
  },
  "body": "JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwo...",
  "isBase64Encoded": true
}
```

### Subida a S3:
```json
{
  "statusCode": 200,
  "body": {
    "message": "PDF generado y subido exitosamente",
    "s3Key": "planilla_1234567890.pdf",
    "bucket": "mi-bucket-planillas"
  }
}
```
